<template>
  <div class="week-view">
    <!-- Time column and day headers -->
    <div class="week-header">
      <div class="time-column-header"></div>
      <div
        v-for="day in weekDays"
        :key="day.date.toISOString()"
        class="day-header"
        :class="{ today: day.isToday }"
        @click="onDayClick(day.date)"
      >
        <div class="day-name">{{ day.name }}</div>
        <div class="day-number">{{ day.number }}</div>
      </div>
    </div>

    <!-- Time grid -->
    <div class="week-grid">
      <div class="time-column">
        <div v-for="hour in hours" :key="hour" class="time-slot">
          {{ formatHour(hour) }}
        </div>
      </div>

      <!-- Day columns -->
      <div
        v-for="day in weekDays"
        :key="day.date.toISOString()"
        class="day-column"
        @click="onDayClick(day.date)"
      >
        <!-- Hour slots -->
        <div
          v-for="hour in hours"
          :key="hour"
          class="hour-slot"
          :class="{ 'current-hour': isCurrentHour(day.date, hour) }"
        ></div>

        <!-- Events -->
        <div
          v-for="event in day.events"
          :key="event.id"
          class="event-item"
          :class="`event-${getEventTypeOption(event.type)?.color}`"
          :style="getEventStyle(event)"
          @click.stop="onEventClick(event)"
        >
          <div class="event-time">{{ formatEventTime(event.startDate, event.endDate) }}</div>
          <div class="event-title">{{ event.title }}</div>
          <div v-if="event.patientName" class="event-patient">{{ event.patientName }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { Event } from 'src/models/event';
import { getEventTypeOption, formatEventTime } from 'src/models/event';

interface Props {
  currentDate: Date;
  events: Event[];
}

interface Emits {
  (e: 'date-clicked', date: Date): void;
  (e: 'event-clicked', event: Event): void;
}

interface WeekDay {
  date: Date;
  name: string;
  number: number;
  isToday: boolean;
  events: Event[];
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// Constants
const hours = Array.from({ length: 24 }, (_, i) => i);

// Computed
const weekDays = computed(() => {
  const startOfWeek = getWeekStart(props.currentDate);
  const days: WeekDay[] = [];

  for (let i = 0; i < 7; i++) {
    const date = new Date(startOfWeek);
    date.setDate(date.getDate() + i);

    days.push({
      date,
      name: date.toLocaleDateString('pt-BR', { weekday: 'short' }),
      number: date.getDate(),
      isToday: isToday(date),
      events: getEventsForDate(date),
    });
  }

  return days;
});

// Methods
const getWeekStart = (date: Date): Date => {
  const start = new Date(date);
  const day = start.getDay();
  const diff = start.getDate() - day;
  return new Date(start.setDate(diff));
};

const isToday = (date: Date): boolean => {
  const today = new Date();
  return date.toDateString() === today.toDateString();
};

const isCurrentHour = (date: Date, hour: number): boolean => {
  const now = new Date();
  return isToday(date) && now.getHours() === hour;
};

const getEventsForDate = (date: Date): Event[] => {
  return props.events.filter((event) => {
    const eventDate = new Date(event.startDate);
    return eventDate.toDateString() === date.toDateString();
  });
};

const formatHour = (hour: number): string => {
  return `${hour.toString().padStart(2, '0')}:00`;
};

const getEventStyle = (event: Event): Record<string, string> => {
  const startDate = new Date(event.startDate);
  const endDate = new Date(event.endDate);

  const startHour = startDate.getHours();
  const startMinute = startDate.getMinutes();
  const endHour = endDate.getHours();
  const endMinute = endDate.getMinutes();

  const startPosition = (startHour + startMinute / 60) * 60; // 60px per hour
  const duration = (endHour + endMinute / 60 - (startHour + startMinute / 60)) * 60;

  return {
    top: `${startPosition}px`,
    height: `${Math.max(duration, 30)}px`, // Minimum 30px height
    left: '2px',
    right: '2px',
  };
};

const onDayClick = (date: Date) => {
  emit('date-clicked', date);
};

const onEventClick = (event: Event) => {
  emit('event-clicked', event);
};
</script>

<style lang="scss" scoped>
.week-view {
  .week-header {
    display: flex;
    border-bottom: 1px solid #e0e0e0;

    .time-column-header {
      width: 60px;
      flex-shrink: 0;
    }

    .day-header {
      flex: 1;
      padding: 12px 8px;
      text-align: center;
      cursor: pointer;
      border-right: 1px solid #e0e0e0;
      transition: background-color 0.2s;

      &:hover {
        background-color: rgba(25, 118, 210, 0.05);
      }

      &.today {
        background-color: rgba(25, 118, 210, 0.1);

        .day-number {
          background-color: var(--q-primary);
          color: white;
          border-radius: 50%;
          width: 28px;
          height: 28px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto;
          font-weight: bold;
        }
      }

      &:last-child {
        border-right: none;
      }

      .day-name {
        font-size: 12px;
        color: #666;
        text-transform: uppercase;
      }

      .day-number {
        font-size: 16px;
        font-weight: 500;
        margin-top: 4px;
      }
    }
  }

  .week-grid {
    display: flex;
    position: relative;

    .time-column {
      width: 60px;
      flex-shrink: 0;
      border-right: 1px solid #e0e0e0;

      .time-slot {
        height: 60px;
        padding: 4px 8px;
        font-size: 12px;
        color: #666;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        align-items: flex-start;
      }
    }

    .day-column {
      flex: 1;
      position: relative;
      border-right: 1px solid #e0e0e0;
      cursor: pointer;

      &:last-child {
        border-right: none;
      }

      .hour-slot {
        height: 60px;
        border-bottom: 1px solid #f0f0f0;

        &.current-hour {
          background-color: rgba(25, 118, 210, 0.05);
        }
      }

      .event-item {
        position: absolute;
        background-color: var(--q-primary);
        color: white;
        padding: 4px 6px;
        border-radius: 4px;
        font-size: 12px;
        cursor: pointer;
        z-index: 1;
        overflow: hidden;
        transition: opacity 0.2s;

        &:hover {
          opacity: 0.9;
        }

        .event-time {
          font-size: 10px;
          opacity: 0.9;
          margin-bottom: 2px;
        }

        .event-title {
          font-weight: 500;
          margin-bottom: 2px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .event-patient {
          font-size: 10px;
          opacity: 0.9;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        // Event type colors
        &.event-primary {
          background-color: var(--q-primary);
        }
        &.event-secondary {
          background-color: var(--q-secondary);
        }
        &.event-accent {
          background-color: var(--q-accent);
        }
        &.event-info {
          background-color: var(--q-info);
        }
        &.event-warning {
          background-color: var(--q-warning);
        }
        &.event-positive {
          background-color: var(--q-positive);
        }
        &.event-negative {
          background-color: var(--q-negative);
        }
        &.event-grey {
          background-color: #9e9e9e;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .week-view {
    .week-header {
      .time-column-header {
        width: 40px;
      }

      .day-header {
        padding: 8px 4px;

        .day-name {
          font-size: 10px;
        }

        .day-number {
          font-size: 14px;
        }
      }
    }

    .week-grid {
      .time-column {
        width: 40px;

        .time-slot {
          height: 40px;
          font-size: 10px;
        }
      }

      .day-column {
        .hour-slot {
          height: 40px;
        }

        .event-item {
          font-size: 10px;
          padding: 2px 4px;
        }
      }
    }
  }
}
</style>
