export interface Event {
  id: string;
  title: string;
  description?: string;
  startDate: string; // ISO string
  endDate: string; // ISO string
  patientId?: string;
  patientName?: string;
  type: string; // Now references CustomEventType.id
  status: EventStatus;
  location?: string;
  notes?: string;
  guests?: string[]; // Array of guest emails or names
  recurrence?: EventRecurrence;
  parentEventId?: string; // For recurring events
  createdAt?: string;
  updatedAt?: string;
}

export enum EventType {
  CONSULTATION = 'consultation',
  FOLLOW_UP = 'followUp',
  PROCEDURE = 'procedure',
  EXAM = 'exam',
  SURGERY = 'surgery',
  MEETING = 'meeting',
  OTHER = 'other',
}

export enum EventStatus {
  SCHEDULED = 'scheduled',
  CONFIRMED = 'confirmed',
  IN_PROGRESS = 'inProgress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  NO_SHOW = 'noShow',
}

export enum RecurrenceType {
  NONE = 'none',
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  YEARLY = 'yearly',
}

export interface EventRecurrence {
  type: RecurrenceType;
  interval: number; // Every X days/weeks/months/years
  endDate?: string; // When to stop recurring
  occurrences?: number; // How many times to repeat
  daysOfWeek?: number[]; // For weekly recurrence (0 = Sunday, 1 = Monday, etc.)
}

export interface EventFormData {
  title: string;
  description?: string;
  startDate: string;
  endDate: string;
  patientId?: string;
  type: string; // Now references CustomEventType.id
  status: EventStatus;
  location?: string;
  notes?: string;
  guests?: string[];
  recurrence?: EventRecurrence;
}

export interface EventFilters {
  search?: string;
  type?: string; // Now references CustomEventType.id
  status?: EventStatus;
  patientId?: string;
  startDate?: string;
  endDate?: string;
}

export interface EventListResponse {
  events: Event[];
  total: number;
  page: number;
  limit: number;
}

// Calendar view types
export enum CalendarView {
  MONTH = 'month',
  WEEK = 'week',
  DAY = 'day',
}

// Event type options for forms
export const EVENT_TYPE_OPTIONS = [
  { value: 'CONSULTATION', label: 'Consulta', icon: 'medical_services', color: 'primary' },
  { value: 'FOLLOW_UP', label: 'Retorno', icon: 'assignment', color: 'secondary' },
  { value: 'PROCEDURE', label: 'Procedimento', icon: 'healing', color: 'accent' },
  { value: 'EXAM', label: 'Exame', icon: 'science', color: 'info' },
  { value: 'SURGERY', label: 'Cirurgia', icon: 'local_hospital', color: 'warning' },
  { value: 'MEETING', label: 'Reunião', icon: 'groups', color: 'positive' },
  { value: 'OTHER', label: 'Outro', icon: 'event', color: 'grey' },
];

// Event status options for forms
export const EVENT_STATUS_OPTIONS = [
  { value: EventStatus.SCHEDULED, label: 'Agendado', color: 'blue' },
  { value: EventStatus.CONFIRMED, label: 'Confirmado', color: 'green' },
  { value: EventStatus.IN_PROGRESS, label: 'Em Andamento', color: 'orange' },
  { value: EventStatus.COMPLETED, label: 'Concluído', color: 'positive' },
  { value: EventStatus.CANCELLED, label: 'Cancelado', color: 'negative' },
  { value: EventStatus.NO_SHOW, label: 'Faltou', color: 'red' },
];

// Recurrence type options for forms
export const RECURRENCE_TYPE_OPTIONS = [
  { value: RecurrenceType.NONE, label: 'Não repetir' },
  { value: RecurrenceType.DAILY, label: 'Diariamente' },
  { value: RecurrenceType.WEEKLY, label: 'Semanalmente' },
  { value: RecurrenceType.MONTHLY, label: 'Mensalmente' },
  { value: RecurrenceType.YEARLY, label: 'Anualmente' },
];

// Days of week for weekly recurrence
export const DAYS_OF_WEEK_OPTIONS = [
  { value: 0, label: 'Domingo', short: 'Dom' },
  { value: 1, label: 'Segunda', short: 'Seg' },
  { value: 2, label: 'Terça', short: 'Ter' },
  { value: 3, label: 'Quarta', short: 'Qua' },
  { value: 4, label: 'Quinta', short: 'Qui' },
  { value: 5, label: 'Sexta', short: 'Sex' },
  { value: 6, label: 'Sábado', short: 'Sáb' },
];

// Helper functions
export const getEventTypeOption = (type: string) => {
  return EVENT_TYPE_OPTIONS.find((option) => option.value === type);
};

// Helper function to get event type info from custom types
export const getEventTypeInfo = (
  typeId: string,
  eventTypes: Array<{ id: string; name: string; icon: string; color: string }>,
) => {
  const customType = eventTypes.find((t) => t.id === typeId);
  if (customType) {
    return {
      label: customType.name,
      icon: customType.icon,
      color: customType.color,
    };
  }

  // Fallback to legacy enum types
  const legacyType = getEventTypeOption(typeId as EventType);
  return legacyType || { label: 'Desconhecido', icon: 'event', color: 'grey' };
};

export const getEventStatusOption = (status: EventStatus) => {
  return EVENT_STATUS_OPTIONS.find((option) => option.value === status);
};

export const formatEventTime = (startDate: string, endDate: string): string => {
  const start = new Date(startDate);
  const end = new Date(endDate);

  const startTime = start.toLocaleTimeString('pt-BR', {
    hour: '2-digit',
    minute: '2-digit',
  });

  const endTime = end.toLocaleTimeString('pt-BR', {
    hour: '2-digit',
    minute: '2-digit',
  });

  return `${startTime} - ${endTime}`;
};

export const getShortEventDescription = (event: Event, maxLength: number = 8): string => {
  const title = event.title || 'Sem título';
  if (title.length <= maxLength) {
    return title;
  }
  return title.substring(0, maxLength - 3) + '...';
};

export const isEventToday = (event: Event): boolean => {
  const today = new Date();
  const eventDate = new Date(event.startDate);

  return today.toDateString() === eventDate.toDateString();
};

export const isEventInDateRange = (event: Event, startDate: Date, endDate: Date): boolean => {
  const eventStart = new Date(event.startDate);
  const eventEnd = new Date(event.endDate);

  return (
    (eventStart >= startDate && eventStart <= endDate) ||
    (eventEnd >= startDate && eventEnd <= endDate) ||
    (eventStart <= startDate && eventEnd >= endDate)
  );
};
