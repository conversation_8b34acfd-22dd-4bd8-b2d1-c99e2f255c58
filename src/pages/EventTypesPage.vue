<template>
  <q-page class="event-types-page">
    <!-- Header -->
    <div class="row items-center justify-between q-mb-lg">
      <div>
        <h4 class="q-ma-none">Tipos de Eventos</h4>
        <p class="text-grey-6 q-ma-none">
          Gerencie os tipos de eventos disponíveis para agendamento
        </p>
      </div>

      <q-btn color="primary" icon="add" label="Novo Tipo" @click="openCreateModal" />
    </div>

    <!-- Filters -->
    <q-card flat bordered class="q-mb-lg">
      <q-card-section>
        <div class="row q-gutter-md items-end">
          <div class="col-12 col-md-4">
            <q-input
              v-model="searchFilter"
              label="Buscar tipos"
              outlined
              dense
              clearable
              debounce="300"
              @update:model-value="onFilterChange"
            >
              <template v-slot:prepend>
                <q-icon name="search" />
              </template>
            </q-input>
          </div>

          <div class="col-12 col-md-3">
            <q-select
              v-model="statusFilter"
              :options="statusOptions"
              label="Status"
              outlined
              dense
              clearable
              emit-value
              map-options
              @update:model-value="onFilterChange"
            />
          </div>

          <div class="col-12 col-md-2">
            <q-btn flat icon="refresh" label="Atualizar" @click="refreshData" :loading="loading" />
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- Event Types List -->
    <q-card flat bordered>
      <q-card-section class="q-pa-none">
        <q-table
          :rows="filteredEventTypes"
          :columns="columns"
          row-key="id"
          :loading="loading"
          :pagination="{ rowsPerPage: 10 }"
          flat
        >
          <!-- Icon column -->
          <template v-slot:body-cell-icon="props">
            <q-td :props="props">
              <q-icon :name="props.value" :color="props.row.color" size="md" />
            </q-td>
          </template>

          <!-- Name column -->
          <template v-slot:body-cell-name="props">
            <q-td :props="props">
              <div>
                <div class="text-weight-medium">{{ props.value }}</div>
                <div v-if="props.row.description" class="text-caption text-grey-6">
                  {{ props.row.description }}
                </div>
              </div>
            </q-td>
          </template>

          <!-- Color column -->
          <template v-slot:body-cell-color="props">
            <q-td :props="props">
              <q-chip :color="props.value" text-color="white" size="sm">
                {{ getColorOption(props.value)?.label || props.value }}
              </q-chip>
            </q-td>
          </template>

          <!-- Status column -->
          <template v-slot:body-cell-isActive="props">
            <q-td :props="props">
              <q-chip :color="props.value ? 'positive' : 'negative'" text-color="white" size="sm">
                {{ props.value ? 'Ativo' : 'Inativo' }}
              </q-chip>
            </q-td>
          </template>

          <!-- Type column -->
          <template v-slot:body-cell-isDefault="props">
            <q-td :props="props">
              <q-chip :color="props.value ? 'info' : 'grey'" text-color="white" size="sm">
                {{ props.value ? 'Padrão' : 'Personalizado' }}
              </q-chip>
            </q-td>
          </template>

          <!-- Actions column -->
          <template v-slot:body-cell-actions="props">
            <q-td :props="props">
              <div class="row q-gutter-xs">
                <q-btn
                  flat
                  round
                  dense
                  icon="edit"
                  color="primary"
                  @click="editEventType(props.row)"
                >
                  <q-tooltip>Editar</q-tooltip>
                </q-btn>

                <q-btn
                  flat
                  round
                  dense
                  icon="content_copy"
                  color="secondary"
                  @click="duplicateEventType(props.row)"
                >
                  <q-tooltip>Duplicar</q-tooltip>
                </q-btn>

                <q-btn
                  flat
                  round
                  dense
                  :icon="props.row.isActive ? 'visibility_off' : 'visibility'"
                  :color="props.row.isActive ? 'warning' : 'positive'"
                  @click="toggleStatus(props.row)"
                  :disable="props.row.isDefault && props.row.isActive"
                >
                  <q-tooltip>
                    {{ props.row.isActive ? 'Desativar' : 'Ativar' }}
                  </q-tooltip>
                </q-btn>

                <q-btn
                  flat
                  round
                  dense
                  icon="delete"
                  color="negative"
                  @click="deleteEventType(props.row)"
                  :disable="props.row.isDefault"
                >
                  <q-tooltip>Excluir</q-tooltip>
                </q-btn>
              </div>
            </q-td>
          </template>
        </q-table>
      </q-card-section>
    </q-card>

    <!-- Event Type Form Modal -->
    <EventTypeFormModal
      v-model="showFormModal"
      :event-type="selectedEventType"
      @saved="onEventTypeSaved"
    />
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useQuasar } from 'quasar';
import { useEventTypeStore } from 'src/stores/event-type-store';
import type { CustomEventType } from 'src/models/event-type';
import { getColorOption } from 'src/models/event-type';

const $q = useQuasar();
const eventTypeStore = useEventTypeStore();

// State
const searchFilter = ref('');
const statusFilter = ref<boolean | undefined>(undefined);
const showFormModal = ref(false);
const selectedEventType = ref<CustomEventType | undefined>(undefined);

// Computed
const loading = computed(() => eventTypeStore.loading);

const statusOptions = [
  { label: 'Ativo', value: true },
  { label: 'Inativo', value: false },
];

const columns = [
  {
    name: 'icon',
    label: 'Ícone',
    field: 'icon',
    align: 'center' as const,
    sortable: false,
  },
  {
    name: 'name',
    label: 'Nome',
    field: 'name',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'color',
    label: 'Cor',
    field: 'color',
    align: 'center' as const,
    sortable: false,
  },
  {
    name: 'isActive',
    label: 'Status',
    field: 'isActive',
    align: 'center' as const,
    sortable: true,
  },
  {
    name: 'isDefault',
    label: 'Tipo',
    field: 'isDefault',
    align: 'center' as const,
    sortable: true,
  },
  {
    name: 'actions',
    label: 'Ações',
    field: 'actions',
    align: 'center' as const,
    sortable: false,
  },
];

const filteredEventTypes = computed(() => {
  let types = eventTypeStore.eventTypes;

  if (searchFilter.value) {
    const search = searchFilter.value.toLowerCase();
    types = types.filter(
      (type) =>
        type.name.toLowerCase().includes(search) ||
        type.description?.toLowerCase().includes(search),
    );
  }

  if (statusFilter.value !== undefined) {
    types = types.filter((type) => type.isActive === statusFilter.value);
  }

  return types;
});

// Methods
const onFilterChange = () => {
  // Filters are reactive, so this is just for potential future API calls
};

const refreshData = async () => {
  await eventTypeStore.loadEventTypes({
    search: searchFilter.value,
    isActive: !!statusFilter.value,
  });
};

const openCreateModal = () => {
  selectedEventType.value = undefined;
  showFormModal.value = true;
};

const editEventType = (eventType: CustomEventType) => {
  selectedEventType.value = eventType;
  showFormModal.value = true;
};

const duplicateEventType = async (eventType: CustomEventType) => {
  try {
    await eventTypeStore.duplicateEventType(eventType.id);
    $q.notify({
      type: 'positive',
      message: 'Tipo de evento duplicado com sucesso!',
    });
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (error) {
    $q.notify({
      type: 'negative',
      message: 'Erro ao duplicar tipo de evento.',
    });
  }
};

const toggleStatus = async (eventType: CustomEventType) => {
  try {
    await eventTypeStore.toggleEventTypeStatus(eventType.id);
    $q.notify({
      type: 'positive',
      message: `Tipo de evento ${eventType.isActive ? 'desativado' : 'ativado'} com sucesso!`,
    });
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (error) {
    $q.notify({
      type: 'negative',
      message: 'Erro ao alterar status do tipo de evento.',
    });
  }
};

const deleteEventType = (eventType: CustomEventType) => {
  $q.dialog({
    title: 'Confirmar Exclusão',
    message: `Tem certeza que deseja excluir o tipo de evento "${eventType.name}"?`,
    cancel: true,
    persistent: true,
    // eslint-disable-next-line @typescript-eslint/no-misused-promises
  }).onOk(async () => {
    try {
      await eventTypeStore.deleteEventType(eventType.id);
      $q.notify({
        type: 'positive',
        message: 'Tipo de evento excluído com sucesso!',
      });
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
      $q.notify({
        type: 'negative',
        message: 'Erro ao excluir tipo de evento.',
      });
    }
  });
};

const onEventTypeSaved = () => {
  // Data is automatically updated through the store
  $q.notify({
    type: 'positive',
    message: 'Tipo de evento salvo com sucesso!',
  });
};

// Lifecycle
onMounted(() => {
  void refreshData();
});
</script>

<style lang="scss" scoped>
.event-types-page {
  padding: 24px;
}

@media (max-width: 768px) {
  .event-types-page {
    padding: 16px;
  }
}
</style>
